using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Domain.Models;
using Phlex.Core.Apify.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public interface IApifyTaskService
{
    /// <summary>
    /// Creates a task in Apify for the specified journal
    /// </summary>
    /// <param name="journal">The journal to create a task for</param>
    /// <param name="taskName">The name for the task</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The ID of the created task, or empty string if creation failed</returns>
    Task<string> CreateTaskForJournalAsync(Journal journal, string taskName, CancellationToken cancellationToken = default);
}

public class ApifyTaskService : IApifyTaskService
{
    private readonly IApifyClient _apifyClient;
    private readonly ILogger<ApifyTaskService> _logger;

    public ApifyTaskService(IApifyClient apifyClient, ILogger<ApifyTaskService> logger)
    {
        _apifyClient = apifyClient;
        _logger = logger;
    }

    public async Task<string> CreateTaskForJournalAsync(Journal journal, string taskName, CancellationToken cancellationToken = default)
    {
        try
        {
            var urls = new List<string> { journal.Url };
            var taskId = await _apifyClient.CreateTaskAsync(taskName, urls, 1, cancellationToken);
            
            _logger.LogInformation("Created Apify task '{TaskName}' with ID '{TaskId}' for journal '{JournalName}'", 
                taskName, taskId, journal.Name);
            
            return taskId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create task '{TaskName}' for journal '{JournalName}'", taskName, journal.Name);
            throw;
        }
    }
}
