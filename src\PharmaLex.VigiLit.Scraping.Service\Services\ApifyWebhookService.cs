using Microsoft.Extensions.Logging;
using Phlex.Core.Apify.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public interface IApifyWebhookService
{
    /// <summary>
    /// Creates a webhook for the specified task in Apify
    /// </summary>
    /// <param name="taskId">The ID of the task to create webhook for</param>
    /// <param name="webhookUrl">The URL where webhook notifications will be sent</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The ID of the created webhook, or empty string if creation failed</returns>
    Task<string> CreateWebhookForTaskAsync(string taskId, string webhookUrl, CancellationToken cancellationToken = default);
}

public class ApifyWebhookService : IApifyWebhookService
{
    private readonly IApifyClient _apifyClient;
    private readonly ILogger<ApifyWebhookService> _logger;

    public ApifyWebhookService(IApifyClient apifyClient, ILogger<ApifyWebhookService> logger)
    {
        _apifyClient = apifyClient;
        _logger = logger;
    }

    public async Task<string> CreateWebhookForTaskAsync(string taskId, string webhookUrl, CancellationToken cancellationToken = default)
    {
        try
        {
            var webhookResponse = await _apifyClient.CreateWebhookAsync(webhookUrl, taskId, cancellationToken);
            
            // Try different possible property names for the webhook ID
            var webhookId = GetWebhookId(webhookResponse);
            
            _logger.LogInformation("Created Apify webhook with ID '{WebhookId}' for task '{TaskId}'", 
                webhookId, taskId);
            
            return webhookId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create webhook for task '{TaskId}'", taskId);
            throw;
        }
    }

    private string GetWebhookId(object webhookResponse)
    {
        // Try to get the ID from the webhook response using reflection
        // Common property names: Id, WebhookId, id, webhookId
        var type = webhookResponse.GetType();
        
        var idProperty = type.GetProperty("Id") ?? 
                        type.GetProperty("WebhookId") ?? 
                        type.GetProperty("id") ?? 
                        type.GetProperty("webhookId");
        
        if (idProperty != null)
        {
            var value = idProperty.GetValue(webhookResponse);
            return value?.ToString() ?? string.Empty;
        }
        
        _logger.LogWarning("Could not find ID property in webhook response of type {Type}", type.Name);
        return string.Empty;
    }
}
