﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Client;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook.Models;
using Task = System.Threading.Tasks.Task;

namespace PharmaLex.VigiLit.Scraping.Service;

public class ApifyNotification : IApifyNotification
{
    private readonly IApifyClient apifyClient;
    private readonly IDataExtractionClient client;
    private readonly IDownloadBlobStorage downloadStorage;
    private readonly ILogger<ApifyNotification> logger;

    public ApifyNotification(
        IApifyClient apifyClient, 
        IDownloadBlobStorage downloadStorage,
        IDataExtractionClient client,
        ILogger<ApifyNotification> logger)
    {
        this.apifyClient = apifyClient;
        this.downloadStorage = downloadStorage;
        this.client = client;
        this.logger = logger;
    }

    public async Task RunSucceeded(ApifyWebhookPayload runData)
    {
        if (runData == null || runData.resource == null || 
            string.IsNullOrEmpty(runData.resource.defaultKeyValueStoreId) || 
            string.IsNullOrEmpty(runData.resource.defaultDatasetId))
        {
            logger.LogError("ApifyNotification: Insufficient run data {RunData}", runData);
            return;
        }

        try
        {
            logger.LogInformation("ApifyNotification: TransferFilesAsync started for taskId: {TaskId},DataSetId: {DataSetId},KeyValueStoreId: {KeyValueStoreId}", runData.resource.actorTaskId, runData.resource.defaultDatasetId, runData.resource.defaultKeyValueStoreId);
            var folderName = runData.resource.actorTaskId ?? string.Empty;

            downloadStorage.SetBlobFolderName(folderName);

            await apifyClient.TransferFilesAsync(runData.resource.defaultDatasetId, runData.resource.defaultKeyValueStoreId, downloadStorage);
           
            logger.LogInformation("ApifyNotification: TransferFilesAsync ended for taskId: {TaskId},DataSetId: {DataSetId}, KeyValueStoreId: {KeyValueStoreId}", runData.resource.actorTaskId, runData.resource.defaultDatasetId, runData.resource.defaultKeyValueStoreId);

            await CreateAndSendExtractDataCommand(folderName);

        }
        catch (Exception ex)
        {
            logger.LogError(ex, "ApifyNotification: An error occured: {ErrorMessage}", ex.Message);
        }
    }

    public async Task RunFailed(ApifyWebhookPayload runData)
    {
        if (runData is null || runData.resource is null)
        {
            logger.LogError("ApifyNotification: RunFailed called with insufficient run data {RunData}", runData);
            return;
        }

        try
        {
            var taskId = runData.resource.actorTaskId ?? "";
            var datasetId = runData.resource.defaultDatasetId ?? "";
            var keyValueStoreId = runData.resource.defaultKeyValueStoreId ?? "";
            var createdAt = runData.createdAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "";

            logger.LogError("ApifyNotification: Scraping run failure - TaskId: {TaskId}, DataSetId: {DataSetId}, KeyValueStoreId: {KeyValueStoreId}, Timestamp: {CreatedAt}",
                taskId, datasetId, keyValueStoreId, createdAt);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "ApifyNotification: An error occurred while processing run failure: {ErrorMessage}", ex.Message);
        }

        await Task.CompletedTask;
    }

    private async Task CreateAndSendExtractDataCommand(string folderName) 
    {
        // Get full blob paths (e.g., ["scraping/batch123/file1.pdf", "scraping/batch123/file2.pdf"])
        var blobPaths = await downloadStorage.GetBlobPathsAsync(folderName);

        // this is temporary change, batches will be organized by URL
        var batchId = Guid.NewGuid();

        foreach (var blobPath in blobPaths)
        {
            var command = new ExtractDataCommand()
            {
                BatchId = batchId.ToString(),
                FileName = blobPath,
                Source = Source.File,
                CorrelationId = Guid.NewGuid()
            };
            await client.Send(command);
        }
    }
}