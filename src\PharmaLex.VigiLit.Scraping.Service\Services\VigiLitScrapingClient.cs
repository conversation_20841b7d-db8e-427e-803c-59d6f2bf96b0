using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.ContractManagement.Ui.Journals;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class VigiLitScrapingClient : IVigiLitScrapingClient
{
    private readonly IJournalRepository _journalRepository;
    private readonly ILogger<VigiLitScrapingClient> _logger;
    private readonly IApifyTaskService _taskService;
    private readonly IApifyScheduleService _scheduleService;
    private readonly IApifyWebhookService _webhookService;
    private readonly IScrapingConfigurationService _configurationService;

    public VigiLitScrapingClient(
        IJournalRepository journalRepository,
        ILogger<VigiLitScrapingClient> logger,
        IApifyTaskService taskService,
        IApifyScheduleService scheduleService,
        IApifyWebhookService webhookService,
        IScrapingConfigurationService configurationService)
    {
        _journalRepository = journalRepository;
        _logger = logger;
        _taskService = taskService;
        _scheduleService = scheduleService;
        _webhookService = webhookService;
        _configurationService = configurationService;
    }

    public async Task<RestoreSchedulesResult> RestoreSchedulesAsync(CancellationToken cancellationToken = default)
    {
        var result = new RestoreSchedulesResult();
        
        try
        {
            _logger.LogInformation("Starting restoration of Apify schedules for existing journals");

            // Get all enabled journals with schedules
            var journals = await _journalRepository.GetAll();
            var enabledJournalsWithSchedules = journals
                .Where(j => j.Enabled && !string.IsNullOrEmpty(j.CronExpression))
                .ToList();

            result.JournalsProcessed = enabledJournalsWithSchedules.Count;
            _logger.LogInformation("Found {Count} enabled journals with schedules", result.JournalsProcessed);

            if (!enabledJournalsWithSchedules.Any())
            {
                result.Messages.Add("No enabled journals with schedules found");
                return result;
            }

            // Group journals by CronExpression (schedule)
            var journalGroups = enabledJournalsWithSchedules
                .GroupBy(j => j.CronExpression)
                .ToList();

            _logger.LogInformation("Grouped journals into {Count} schedule groups", journalGroups.Count());

            // Get webhook URL from configuration
            var webhookUrl = _configurationService.GetWebhookUrl();
            if (string.IsNullOrEmpty(webhookUrl))
            {
                result.Errors.Add("Webhook URL not configured");
                return result;
            }

            foreach (var group in journalGroups)
            {
                try
                {
                    await ProcessScheduleGroup(group, webhookUrl, result, cancellationToken);
                }
                catch (Exception ex)
                {
                    var errorMessage = $"Error processing schedule group with cron '{group.Key}': {ex.Message}";
                    _logger.LogError(ex, errorMessage);
                    result.Errors.Add(errorMessage);
                }
            }

            _logger.LogInformation("Completed restoration. Schedules: {Schedules}, Tasks: {Tasks}, Webhooks: {Webhooks}, Errors: {Errors}",
                result.SchedulesCreated, result.TasksCreated, result.WebhooksCreated, result.Errors.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during schedule restoration");
            result.Errors.Add($"General error: {ex.Message}");
            return result;
        }
    }

    private async Task ProcessScheduleGroup(
        IGrouping<string, Domain.Models.Journal> group, 
        string webhookUrl, 
        RestoreSchedulesResult result, 
        CancellationToken cancellationToken)
    {
        var cronExpression = group.Key;
        var journals = group.ToList();
        
        _logger.LogInformation("Processing schedule group with cron '{Cron}' containing {Count} journals", 
            cronExpression, journals.Count);

        // Create a task for each journal in this schedule group
        foreach (var journal in journals)
        {
            try
            {
                await ProcessJournal(journal, cronExpression, webhookUrl, result, cancellationToken);
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error processing journal '{journal.Name}': {ex.Message}";
                _logger.LogError(ex, errorMessage);
                result.Errors.Add(errorMessage);
            }
        }
    }

    private async Task ProcessJournal(
        Domain.Models.Journal journal, 
        string cronExpression, 
        string webhookUrl, 
        RestoreSchedulesResult result, 
        CancellationToken cancellationToken)
    {
        var taskName = $"VigiLit_{journal.Name}_{journal.Id}";
        var scheduleName = $"Schedule_{journal.Name}_{journal.Id}";

        _logger.LogInformation("Processing journal '{Name}' (ID: {Id})", journal.Name, journal.Id);

        // Create task for this journal
        var taskId = await _taskService.CreateTaskForJournalAsync(journal, taskName, cancellationToken);
        if (!string.IsNullOrEmpty(taskId))
        {
            result.TasksCreated++;
            result.Messages.Add($"Created task '{taskName}' for journal '{journal.Name}'");

            // Create schedule for this task
            var scheduleId = await _scheduleService.CreateScheduleForTaskAsync(taskId, scheduleName, cronExpression, cancellationToken);
            if (!string.IsNullOrEmpty(scheduleId))
            {
                result.SchedulesCreated++;
                result.Messages.Add($"Created schedule '{scheduleName}' for journal '{journal.Name}'");
            }

            // Create webhook for this task
            var webhookId = await _webhookService.CreateWebhookForTaskAsync(taskId, webhookUrl, cancellationToken);
            if (!string.IsNullOrEmpty(webhookId))
            {
                result.WebhooksCreated++;
                result.Messages.Add($"Created webhook for task '{taskName}'");
            }
        }
    }

}
