using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public interface IScrapingConfigurationService
{
    /// <summary>
    /// Gets the webhook URL for Apify notifications
    /// </summary>
    /// <returns>The webhook URL, or empty string if not configured</returns>
    string GetWebhookUrl();
}

public class ScrapingConfigurationService : IScrapingConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ScrapingConfigurationService> _logger;

    public ScrapingConfigurationService(IConfiguration configuration, ILogger<ScrapingConfigurationService> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public string GetWebhookUrl()
    {
        // Get the webhook URL from configuration
        // This should be the URL where Apify will send webhook notifications
        var baseUrl = _configuration["HostUri"] ?? _configuration["WebsiteUri"];
        if (string.IsNullOrEmpty(baseUrl))
        {
            _logger.LogWarning("No base URL configured for webhooks");
            return string.Empty;
        }

        // Construct the webhook endpoint URL
        var webhookPath = "/api/apify/webhook"; // This should match the webhook controller endpoint
        return $"{baseUrl.TrimEnd('/')}{webhookPath}";
    }
}
