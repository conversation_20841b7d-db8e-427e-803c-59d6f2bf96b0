using Microsoft.Extensions.Logging;
using Phlex.Core.Apify.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public interface IApifyScheduleService
{
    /// <summary>
    /// Creates a schedule for the specified task in Apify
    /// </summary>
    /// <param name="taskId">The ID of the task to schedule</param>
    /// <param name="scheduleName">The name for the schedule</param>
    /// <param name="cronExpression">The cron expression for the schedule</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The ID of the created schedule, or empty string if creation failed</returns>
    Task<string> CreateScheduleForTaskAsync(string taskId, string scheduleName, string cronExpression, CancellationToken cancellationToken = default);
}

public class ApifyScheduleService : IApifyScheduleService
{
    private readonly IApifyClient _apifyClient;
    private readonly ILogger<ApifyScheduleService> _logger;

    public ApifyScheduleService(IApifyClient apifyClient, ILogger<ApifyScheduleService> logger)
    {
        _apifyClient = apifyClient;
        _logger = logger;
    }

    public async Task<string> CreateScheduleForTaskAsync(string taskId, string scheduleName, string cronExpression, CancellationToken cancellationToken = default)
    {
        try
        {
            var timeZone = "UTC"; // Default timezone
            var scheduleResponse = await _apifyClient.CreateSchedulesAsync(taskId, scheduleName, cronExpression, timeZone, cancellationToken);
            
            // Try different possible property names for the schedule ID
            var scheduleId = GetScheduleId(scheduleResponse);
            
            _logger.LogInformation("Created Apify schedule '{ScheduleName}' with ID '{ScheduleId}' for task '{TaskId}'", 
                scheduleName, scheduleId, taskId);
            
            return scheduleId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create schedule '{ScheduleName}' for task '{TaskId}'", scheduleName, taskId);
            throw;
        }
    }

    private string GetScheduleId(object scheduleResponse)
    {
        // Try to get the ID from the schedule response using reflection
        // Common property names: Id, ScheduleId, id, scheduleId
        var type = scheduleResponse.GetType();
        
        var idProperty = type.GetProperty("Id") ?? 
                        type.GetProperty("ScheduleId") ?? 
                        type.GetProperty("id") ?? 
                        type.GetProperty("scheduleId");
        
        if (idProperty != null)
        {
            var value = idProperty.GetValue(scheduleResponse);
            return value?.ToString() ?? string.Empty;
        }
        
        _logger.LogWarning("Could not find ID property in schedule response of type {Type}", type.Name);
        return string.Empty;
    }
}
